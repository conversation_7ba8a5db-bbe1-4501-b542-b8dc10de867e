/// Workout Program API Service - GymKod Pro Mobile
///
/// Bu service mobil antrenman programı API'leri ile iletişim kurar.
/// Referans: Backend MobileWorkoutProgramController
library;

import '../../../../core/models/api_response.dart';
import '../../../../core/services/api_service.dart';
import '../../../../core/services/logging_service.dart';
import '../models/workout_program_models.dart';

/// Workout Program API Service Interface
abstract class WorkoutProgramApiService {
  /// Kullanıcının aktif antrenman programlarını al
  Future<ApiResponse<List<MemberActiveWorkoutProgram>>> getMyActivePrograms();

  /// Belirli bir programın detaylarını al
  Future<ApiResponse<WorkoutProgramDetail>> getProgramDetail(int programTemplateId);

  /// Kullanıcının üyelik durumunu kontrol et
  Future<ApiResponse<MembershipStatus>> getMembershipStatus();
}

/// Workout Program API Service Implementation
class WorkoutProgramApiServiceImpl implements WorkoutProgramApiService {
  final ApiService _apiService;

  WorkoutProgramApiServiceImpl(this._apiService);

  @override
  Future<ApiResponse<List<MemberActiveWorkoutProgram>>> getMyActivePrograms() async {
    try {
      LoggingService.authLog('Workout programs request');

      final response = await _apiService.get<Map<String, dynamic>>(
        '/mobile/mobileworkoutprogram/my-programs',
      );

      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data!;
        
        if (responseData['success'] == true) {
          final List<dynamic> programsJson = responseData['data'] ?? [];
          final programs = programsJson
              .map((json) => MemberActiveWorkoutProgram.fromJson(json as Map<String, dynamic>))
              .toList();

          LoggingService.authLog('Workout programs success', 
            details: 'Program count: ${programs.length}');

          return ApiResponse.success(
            message: responseData['message'] ?? 'Programlar başarıyla alındı.',
            data: programs,
            extraData: {
              'hasPrograms': responseData['hasPrograms'] ?? false,
            },
          );
        } else {
          LoggingService.authLog('Workout programs failed', details: responseData['message']);
          return ApiResponse.error(
            message: responseData['message'] ?? 'Programlar alınamadı.',
          );
        }
      }

      return ApiResponse.error(
        message: 'Sunucudan geçersiz yanıt alındı.',
      );
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'WorkoutProgramApiService getMyActivePrograms');

      // Network error kontrolü
      if (e.toString().contains('SocketException') || e.toString().contains('TimeoutException')) {
        return ApiResponse.error(
          message: 'İnternet bağlantınızı kontrol edin.',
        );
      }

      // Forbidden error kontrolü
      if (e.toString().contains('403') || e.toString().contains('Forbidden')) {
        return ApiResponse.error(
          message: 'Bu işlem için yetkiniz bulunmuyor.',
        );
      }

      return ApiResponse.error(
        message: 'Antrenman programları alınamadı. Lütfen tekrar deneyin.',
      );
    }
  }

  @override
  Future<ApiResponse<WorkoutProgramDetail>> getProgramDetail(int programTemplateId) async {
    try {
      LoggingService.authLog('Program detail request', details: 'Program ID: $programTemplateId');

      final response = await _apiService.get<Map<String, dynamic>>(
        '/mobile/mobileworkoutprogram/program-detail/$programTemplateId',
      );

      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data!;
        
        if (responseData['success'] == true) {
          final programJson = responseData['data'] as Map<String, dynamic>;
          final program = WorkoutProgramDetail.fromJson(programJson);

          LoggingService.authLog('Program detail success', 
            details: 'Program: ${program.programName}, Days: ${program.dayCount}');

          return ApiResponse.success(
            message: responseData['message'] ?? 'Program detayları başarıyla alındı.',
            data: program,
          );
        } else {
          LoggingService.authLog('Program detail failed', details: responseData['message']);
          return ApiResponse.error(
            message: responseData['message'] ?? 'Program detayları alınamadı.',
          );
        }
      }

      return ApiResponse.error(
        message: 'Sunucudan geçersiz yanıt alındı.',
      );
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'WorkoutProgramApiService getProgramDetail');

      // Forbidden error kontrolü
      if (e.toString().contains('403') || e.toString().contains('Forbidden')) {
        return ApiResponse.error(
          message: 'Bu programa erişim yetkiniz bulunmuyor.',
        );
      }

      return ApiResponse.error(
        message: 'Program detayları alınamadı. Lütfen tekrar deneyin.',
      );
    }
  }

  @override
  Future<ApiResponse<MembershipStatus>> getMembershipStatus() async {
    try {
      LoggingService.authLog('Membership status request');

      final response = await _apiService.get<Map<String, dynamic>>(
        '/mobile/mobileworkoutprogram/membership-status',
      );

      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data!;
        
        if (responseData['success'] == true) {
          final statusJson = responseData['data'] as Map<String, dynamic>;
          final status = MembershipStatus.fromJson(statusJson);

          LoggingService.authLog('Membership status success', 
            details: 'Member: ${status.isMember}, Programs: ${status.programCount}');

          return ApiResponse.success(
            message: 'Üyelik durumu başarıyla alındı.',
            data: status,
          );
        } else {
          LoggingService.authLog('Membership status failed', details: responseData['message']);
          return ApiResponse.error(
            message: responseData['message'] ?? 'Üyelik durumu alınamadı.',
          );
        }
      }

      return ApiResponse.error(
        message: 'Sunucudan geçersiz yanıt alındı.',
      );
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'WorkoutProgramApiService getMembershipStatus');

      return ApiResponse.error(
        message: 'Üyelik durumu kontrol edilemedi. Lütfen tekrar deneyin.',
      );
    }
  }
}

/// Workout Program Repository Interface
abstract class WorkoutProgramRepository {
  /// Kullanıcının aktif antrenman programlarını al
  Future<ApiResponse<List<MemberActiveWorkoutProgram>>> getMyActivePrograms();

  /// Belirli bir programın detaylarını al
  Future<ApiResponse<WorkoutProgramDetail>> getProgramDetail(int programTemplateId);

  /// Kullanıcının üyelik durumunu kontrol et
  Future<ApiResponse<MembershipStatus>> getMembershipStatus();
}

/// Workout Program Repository Implementation
class WorkoutProgramRepositoryImpl implements WorkoutProgramRepository {
  final WorkoutProgramApiService _workoutProgramApiService;

  WorkoutProgramRepositoryImpl(this._workoutProgramApiService);

  @override
  Future<ApiResponse<List<MemberActiveWorkoutProgram>>> getMyActivePrograms() async {
    try {
      LoggingService.authLog('Workout programs repository request');

      final result = await _workoutProgramApiService.getMyActivePrograms();

      if (result.isSuccess && result.data != null) {
        LoggingService.authLog('Workout programs repository success',
          details: 'Program count: ${result.data!.length}');

        return result;
      } else {
        LoggingService.authLog('Workout programs repository failed', details: result.message);
        return result;
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'WorkoutProgramRepository getMyActivePrograms');

      return ApiResponse.error(
        message: 'Antrenman programları alınamadı. Lütfen tekrar deneyin.',
      );
    }
  }

  @override
  Future<ApiResponse<WorkoutProgramDetail>> getProgramDetail(int programTemplateId) async {
    try {
      LoggingService.authLog('Program detail repository request', details: 'Program ID: $programTemplateId');

      final result = await _workoutProgramApiService.getProgramDetail(programTemplateId);

      if (result.isSuccess && result.data != null) {
        LoggingService.authLog('Program detail repository success',
          details: 'Program: ${result.data!.programName}');

        return result;
      } else {
        LoggingService.authLog('Program detail repository failed', details: result.message);
        return result;
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'WorkoutProgramRepository getProgramDetail');

      return ApiResponse.error(
        message: 'Program detayları alınamadı. Lütfen tekrar deneyin.',
      );
    }
  }

  @override
  Future<ApiResponse<MembershipStatus>> getMembershipStatus() async {
    try {
      LoggingService.authLog('Membership status repository request');

      final result = await _workoutProgramApiService.getMembershipStatus();

      if (result.isSuccess && result.data != null) {
        LoggingService.authLog('Membership status repository success',
          details: 'Status: ${result.data!.message}');

        return result;
      } else {
        LoggingService.authLog('Membership status repository failed', details: result.message);
        return result;
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'WorkoutProgramRepository getMembershipStatus');

      return ApiResponse.error(
        message: 'Üyelik durumu kontrol edilemedi. Lütfen tekrar deneyin.',
      );
    }
  }
}
