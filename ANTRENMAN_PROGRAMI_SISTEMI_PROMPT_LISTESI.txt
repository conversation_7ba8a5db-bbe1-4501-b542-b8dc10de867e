🎯 ANTRENMAN PROGRAMI SİSTEMİ - KAPSAMLI PROMPT LİSTESİ
================================================================

PROJE: GymKod Pro - Multi-tenant Spor Salonu Yönetim Sistemi
HEDEF: 10.000+ kullanıcı için antrenman programı sistemi
YAKLASIM: Backend + Frontend birlikte (test kolaylığı için)

================================================================
PROMPT 1: Egzersiz Kategorileri ve Kapsamlı Egzersiz Veritabanı 
================================================================

AMAÇ:
Sistem genelinde kullanılacak KAPSAMLI egzersiz altyapısını kurmak

SORUN:
Sistem genelinde kullanılabilir egzersiz veritabanı yok
Salon hocaları sadece görüntüleyebileceği için TÜM FİTNESS SPOR SALONLARINA YAPILABİLECEK EGZERSİZLERİ sisteme yüklememiz gerekiyor. türkçe karşılığı mantıklı olan egzersizlerin yanına parantez içinde türkçesini yazalım örneğin push up(şınav) gibi mantıklı türkçe litarütüründe olan kelimelerle uyumlu hareket varsa yanına parantez içinde türkçesini de yaz ki sistemi kullanan kişiler hareketin ingilizcesini bilmiyorsa bile türkçesiyle sorunu kapatabilsin. ingilizce hareketleri türkçeye çevirdiğmiizde anlamsız isim çıkacaksa o hareketlerin yanına türkçesini yazma örneğin Bench Press" (Türkçesi "Bank Presi" anlamsız).
hareketleri ayarlarken makine + dumbell + vücut ağırlığı içeren hareketleri koyma odaklı yap mesela fly olarak dumbell fly var bide kelebek makinesinde fly var
Egzersiz kategorileri sistemi yok
salon sahibi mevcut sistem tarafından tanımlanan egzersizleri yetersiz görüp kendisi eklemek isterse yeni egzersiz ekle butonuna basabilmeli ve sadece ekleyen salon sahibi kendi panelinde görebilmeli örneğin a salonu yeni bir egzersiz eklerse b salonu bu egzersizi görememeli. sadece sistem tarafından ayarlanan egzersizleri herkese görülmeli.
İSTEDİĞİMİZ:
KAPSAMLI egzersiz veritabanı - Fitness salonlarında yapılan TÜM egzersizler
Egzersizleri kategoriye ayıracağız örneğin göğüs bacak sırt omuz tarzında bütün bölgelerin kategorisiyle egzersizleri birleştireceğiz ki spor salonu sahibi filtreleme yapabilsin.
KAPSAM:
ExerciseCategories tablosu
Exercises tablosu
Backend: Egzersiz görüntüleme servisleri (SADECE READ)
Frontend: Admin panelinde egzersiz listesi (sadece görüntüleme + arama + filtreleme)
Kapsamlı Seed Data: Fitness dünyasındaki bilinen tüm egzersizler
TEST:
Admin panelinden egzersizleri kategorilere göre filtreleyip, arama yaparak istediği egzersizi bulabilmeli.

================================================================
PROMPT 2: Antrenman Programı Şablonu Sistemi
================================================================

AMAÇ: Salon sahiplerinin egzersizlerden program oluşturabilmesi. bu sistemi kurma sebebimiz spor salonu hocalarının egzersizleri program haline getirip ilerde
salon üyelerine bu programları entegre edip üyelere antrenman programını görebilmelerini sağlamak için. şimdilik aşağıdakileri yapalım.

SORUN:
- Salon sahipleri antrenman programı oluşturamıyor
- Sistem egzersizlerinden program yapma sistemi yok
- Günlere bölerek program organize etme yok
- Her egzersiz için set/tekrar belirleme sistemi yok
- Gün isimlerini özelleştirme yok ("Sırt-Önkol" vb.)

İSTEDİĞİMİZ:
- Admin panelinde "Antrenman Programları" menüsü
- Program şablonu listesi (sadece kendi salonunun programları)
- Yeni program oluşturma sistemi:
  * Program adı girişi (örn: "Başlangıç Fitness", "Kilo Verme Programı")
  * Program açıklaması
  * Deneyim seviyesi seçimi (Başlangıç/Orta/İleri)
  * Hedef seçimi (Kilo Alma/Kilo Verme/Kas Yapma)
  * Program süresi (kaç hafta)
- Gün ekleme sistemi:
  * Gün sayısı belirleme (3 gün, 5 gün vb.)
  * Her güne özel isim verme ("Sırt-Önkol", "Göğüs-Arka Kol" vb.)
  * Dinlenme günü işaretleme
- Egzersiz ekleme sistemi:
  * Sistem egzersizlerinden seçim (dropdown/modal)
  * Her egzersiz için set sayısı girişi
  * Her egzersiz için tekrar sayısı girişi (12, MAX, 12-15 vb.)
  * Egzersiz notları
  * Egzersiz sıralaması (drag&drop)

KAPSAM:
- WorkoutProgramTemplate, WorkoutProgramDay, WorkoutProgramExercise için backend servisleri
- Program şablonu CRUD işlemleri
- Frontend: Program listesi component'i
- Program oluşturma sayfası:
  * Program bilgileri formu
  * Gün ekleme/düzenleme sistemi
  * Egzersiz seçim modal'ı (sistem egzersizlerinden)
  * Set/tekrar girişi
  * Drag&drop sıralama

TEST: Admin "Başlangıç Fitness" programı oluşturup, "Sırt-Önkol" günü ekleyip, sistem egzersizlerinden seçerek set/tekrar belirleyebilmeli. kaydettiğinde panelinde programlarım bölümünde görebilmeli.
gördükten sonra güncelleyebilmeli.

================================================================
PROMPT 3: Program Atama Sistemi 
================================================================

AMAÇ: Üyelere antrenman programı atayabilmek

SORUN:
- Üyelere program atama sistemi yok
- Program atama geçmişi görülmüyor
- Bir üyeye kaç program atandığı bilinmiyor

İSTEDİĞİMİZ:
- Program atama sistemi:
  * Üye seçimi (salon üyelerinden seçim)
  * Program seçimi (kendi salonunun programları)
  * Notlar ekleyebilme
- Bir üyeye istediği kadar program atayabilme
- Program atama geçmişi

KAPSAM:
- MemberWorkoutPrograms tablosu (üye-program ilişkisi)
- Backend: Program atama servisleri
- Frontend: Program atama paneli (üye seçimi + program seçimi + tarih)
- Atama geçmişi görüntüleme

TEST: Admin üyeye program atayıp, üye detayında aktif programları görebilmeli.

================================================================
PROMPT 4A: Temel Mobil API ve User-Member İlişkisi (ÖNCE)
================================================================

AMAÇ: Mobil uygulama için temel API altyapısını kurmak ve program görüntüleme

SORUN:
- Mobil uygulamada antrenman programı sayfası boş (geçici içerik)
- User-Member ilişkisi üzerinden program getirme sistemi yok
- Üyeler atanmış programlarını göremiyor
- Program yoksa uygun mesaj gösterilmiyor

İSTEDİĞİMİZ:
- User ID → Member ID ilişkisi kurma
- Mobil API endpoint'leri:
  * Kullanıcının aktif programlarını getirme
  * Program detaylarını getirme (günler + egzersizler)
- Mobil antrenman programı sayfası yeniden tasarımı:
  * Program yoksa: "Salon yöneticinize danışın" mesajı
  * Program varsa: Detaylı program görüntüleme
  * Günlük antrenman planları
  * Egzersiz detayları (set/tekrar/notlar)

KAPSAM:
- Backend: MobileWorkoutProgramController oluşturma
- Mobil API servisleri (User-Member ilişkisi)
- Mobil UI: WorkoutProgramPage yeniden tasarımı
- Responsive tasarım (karanlık mod uyumlu)
- Performans optimizasyonu (cache, indexler)

TEST: 
- Üye mobil uygulamaya giriş yapar
- Program atanmışsa: Detaylı program görür
- Program atanmamışsa: "Salon yöneticinize danışın" mesajı görür

================================================================
PROMPT 4B: İlerleme Takip Sistemi ve Gamification (SONRA)
================================================================

AMAÇ: Kullanıcı motivasyonunu artırmak için ilerleme takip sistemi

SORUN:
- Üyeler egzersiz tamamlama durumunu takip edemiyor
- İlerleme istatistikleri yok
- Motivasyon sistemi eksik

İSTEDİĞİMİZ:
- Egzersiz tamamlama sistemi:
  * Her egzersiz için "Tamamlandı" butonu
  * Set bazlı tamamlama takibi
  * Günlük antrenman tamamlama oranı
- İlerleme istatistikleri:
  * Haftalık/aylık ilerleme grafikleri
  * Tamamlanan egzersiz sayıları
  * Antrenman sıklığı analizi
- Başarım sistemi:
  * Rozet/puan sistemi
  * Milestone'lar (7 gün üst üste, 30 gün vb.)
  * Motivasyon bildirimleri

KAPSAM:
- Yeni tablolar: WorkoutProgress, WorkoutSessions, MemberAchievements
- Backend: Progress tracking servisleri
- Mobil UI: İlerleme dashboard'u, achievement badges
- Charts/graphs entegrasyonu
- Notification sistemi

TEST:
- Üye egzersizi tamamlar, ilerleme kaydedilir
- İstatistik sayfasında grafikleri görür
- Başarım kazandığında bildirim alır

================================================================
PERFORMANS OPTİMİZASYONU:
================================================================
buradaki yazıları bütün promptlarda uygulamanı istiyorum.
veritabanı tablolarını oluştururken index kullanılması gereken yer varsa migration içinde indexi de ayarla amacımız 10000 kullanıcının sıkıntısız kasmadan ve donma olmadan sistemi kullanabilmesini sağlamak.

cache sistemini de en uygun nasılsa hangi promptta olursak olalım eklenmesi gereken yere ekle.
pagination gerekiyorsa yap lazyloading gereken kısımlar varsa uygula
Performance optimizasyonunu da her promptta uygula

================================================================
TEST STRATEJİSİ:
================================================================

Her prompt tamamlandıktan sonra projeleri buildle ve kodlarda hata var mı kontrol et daha sonra bana rapor çıkar ve sistemi test etmemi bekle. 
HEDEF: Türkiye çapında 10.000+ kullanıcının sorunsuz kullanabileceği sistem

================================================================
⚙️ İŞ KURALLARI:
================================================================

- Bir üyeye salon sahibi istediği kadar program atayabilir
- Egzersizler sistem genelinde tanımlı (salon sahipleri eğer aradığı bir hareket yoksa ve eksik bir hareket gördüyse kendisi sistemine egzersizi ekleyebilir ama bu sadece salon sahibi tarafından kendi panelinde ona görünür diğer salon sahiplerine görünmez.)
- Hazır programlar sistem genelinde paylaşılır
- Admin ve owner yetkili kullanıcılar program oluşturabilir
- Program atama Member tablosu üzerinden yapılır (salon sahipleri kendi üyelerine atar)
- Mobilde User login sistemi kullanılır, Member ilişkisi ile programlar getirilir

================================================================
PROMPT YÖNETİM SİSTEMİ:
================================================================

TAMAMLANAN PROMPTLAR:
- [x] PROMPT 1: Egzersiz Kategorileri ve Kapsamlı Egzersiz Veritabanı 
- [x] PROMPT 2: Antrenman Programı Şablonu Sistemi
- [x] PROMPT 3: Program Atama Sistemi
- [ ] PROMPT 4A: Temel Mobil API ve User-Member İlişkisi (ŞU ANDA)
- [ ] PROMPT 4B: İlerleme Takip Sistemi ve Gamification (SONRA)

ŞU ANDA YAPILACAK PROMPT: PROMPT 4A - Temel mobil entegrasyonu tamamlayıp test edeceğiz. Backend kısmını bitirince projeyi buildle ve bittiğine dair rapor ver daha sonra mobil konusunda benden onay bekle.
