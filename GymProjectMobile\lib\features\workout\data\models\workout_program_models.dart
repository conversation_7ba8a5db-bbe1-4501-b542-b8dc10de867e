/// Workout Program Models - GymKod Pro Mobile
///
/// Bu modeller backend'deki antrenman programı DTO'larından uyarlanmıştır.
/// Referans: Backend MemberActiveWorkoutProgramDto, WorkoutProgramTemplateDto
library;

import 'package:json_annotation/json_annotation.dart';

part 'workout_program_models.g.dart';

/// Üyenin Aktif Antrenman Programı (Backend: MemberActiveWorkoutProgramDto)
@JsonSerializable()
class MemberActiveWorkoutProgram {
  final int memberWorkoutProgramID;
  final int workoutProgramTemplateID;
  final String programName;
  final String? programDescription;
  final String? experienceLevel;
  final String? targetGoal;
  final DateTime startDate;
  final DateTime? endDate;
  final String? notes;
  final int dayCount;
  final int exerciseCount;

  const MemberActiveWorkoutProgram({
    required this.memberWorkoutProgramID,
    required this.workoutProgramTemplateID,
    required this.programName,
    this.programDescription,
    this.experienceLevel,
    this.targetGoal,
    required this.startDate,
    this.endDate,
    this.notes,
    required this.dayCount,
    required this.exerciseCount,
  });

  factory MemberActiveWorkoutProgram.fromJson(Map<String, dynamic> json) =>
      _$MemberActiveWorkoutProgramFromJson(json);

  Map<String, dynamic> toJson() => _$MemberActiveWorkoutProgramToJson(this);

  /// Program süresini hesapla (gün olarak)
  int get programDurationDays {
    if (endDate != null) {
      return endDate!.difference(startDate).inDays;
    }
    return 0; // Süresiz program
  }

  /// Program aktif mi?
  bool get isActive {
    final now = DateTime.now();
    if (endDate != null) {
      return now.isAfter(startDate) && now.isBefore(endDate!);
    }
    return now.isAfter(startDate); // Süresiz program
  }

  /// Deneyim seviyesi rengi
  String get experienceLevelColor {
    switch (experienceLevel?.toLowerCase()) {
      case 'başlangıç':
        return '#4CAF50'; // Yeşil
      case 'orta':
        return '#FF9800'; // Turuncu
      case 'ileri':
        return '#F44336'; // Kırmızı
      default:
        return '#9E9E9E'; // Gri
    }
  }

  /// Hedef ikonu
  String get targetGoalIcon {
    switch (targetGoal?.toLowerCase()) {
      case 'kilo alma':
        return '📈';
      case 'kilo verme':
        return '📉';
      case 'kas yapma':
        return '💪';
      default:
        return '🎯';
    }
  }
}

/// Program Detayı (Backend: WorkoutProgramTemplateDto)
@JsonSerializable()
class WorkoutProgramDetail {
  final int workoutProgramTemplateID;
  final int companyID;
  final String programName;
  final String? description;
  final String? experienceLevel;
  final String? targetGoal;
  final bool? isActive;
  final DateTime? creationDate;
  final int dayCount;
  final List<WorkoutProgramDay> days;

  const WorkoutProgramDetail({
    required this.workoutProgramTemplateID,
    required this.companyID,
    required this.programName,
    this.description,
    this.experienceLevel,
    this.targetGoal,
    this.isActive,
    this.creationDate,
    required this.dayCount,
    required this.days,
  });

  factory WorkoutProgramDetail.fromJson(Map<String, dynamic> json) =>
      _$WorkoutProgramDetailFromJson(json);

  Map<String, dynamic> toJson() => _$WorkoutProgramDetailToJson(this);

  /// Toplam egzersiz sayısı
  int get totalExerciseCount {
    return days.fold(0, (total, day) => total + day.exercises.length);
  }

  /// Antrenman günleri (dinlenme günleri hariç)
  List<WorkoutProgramDay> get workoutDays {
    return days.where((day) => !day.isRestDay).toList();
  }

  /// Dinlenme günleri
  List<WorkoutProgramDay> get restDays {
    return days.where((day) => day.isRestDay).toList();
  }
}

/// Program Günü (Backend: WorkoutProgramDayDto)
@JsonSerializable()
class WorkoutProgramDay {
  final int workoutProgramDayID;
  final int workoutProgramTemplateID;
  final int dayNumber;
  final String dayName;
  final bool isRestDay;
  final DateTime? creationDate;
  final List<WorkoutProgramExercise> exercises;

  const WorkoutProgramDay({
    required this.workoutProgramDayID,
    required this.workoutProgramTemplateID,
    required this.dayNumber,
    required this.dayName,
    required this.isRestDay,
    this.creationDate,
    required this.exercises,
  });

  factory WorkoutProgramDay.fromJson(Map<String, dynamic> json) =>
      _$WorkoutProgramDayFromJson(json);

  Map<String, dynamic> toJson() => _$WorkoutProgramDayToJson(this);

  /// Gün ikonu
  String get dayIcon {
    if (isRestDay) return '😴';
    
    final lowerName = dayName.toLowerCase();
    if (lowerName.contains('göğüs')) return '💪';
    if (lowerName.contains('sırt')) return '🏋️';
    if (lowerName.contains('bacak')) return '🦵';
    if (lowerName.contains('omuz')) return '🤲';
    if (lowerName.contains('kol')) return '💪';
    if (lowerName.contains('karın')) return '🔥';
    if (lowerName.contains('cardio')) return '🏃';
    
    return '🏋️‍♂️';
  }

  /// Tahmini süre (dakika)
  int get estimatedDurationMinutes {
    if (isRestDay) return 0;
    
    // Her egzersiz için ortalama 3-4 dakika hesapla
    return exercises.length * 4;
  }
}

/// Program Egzersizi (Backend: WorkoutProgramExerciseDto)
@JsonSerializable()
class WorkoutProgramExercise {
  final int workoutProgramExerciseID;
  final int workoutProgramDayID;
  final String exerciseType; // "System" veya "Company"
  final int exerciseID;
  final String exerciseName;
  final String? exerciseDescription;
  final String? categoryName;
  final int orderIndex;
  final int sets;
  final String reps;
  final int? restTime;
  final String? notes;
  final DateTime? creationDate;

  const WorkoutProgramExercise({
    required this.workoutProgramExerciseID,
    required this.workoutProgramDayID,
    required this.exerciseType,
    required this.exerciseID,
    required this.exerciseName,
    this.exerciseDescription,
    this.categoryName,
    required this.orderIndex,
    required this.sets,
    required this.reps,
    this.restTime,
    this.notes,
    this.creationDate,
  });

  factory WorkoutProgramExercise.fromJson(Map<String, dynamic> json) =>
      _$WorkoutProgramExerciseFromJson(json);

  Map<String, dynamic> toJson() => _$WorkoutProgramExerciseToJson(this);

  /// Dinlenme süresi formatı
  String get restTimeFormatted {
    if (restTime == null || restTime == 0) return 'Belirtilmemiş';
    
    if (restTime! < 60) {
      return '${restTime}sn';
    } else {
      final minutes = restTime! ~/ 60;
      final seconds = restTime! % 60;
      if (seconds == 0) {
        return '${minutes}dk';
      } else {
        return '${minutes}dk ${seconds}sn';
      }
    }
  }

  /// Kategori ikonu
  String get categoryIcon {
    switch (categoryName?.toLowerCase()) {
      case 'göğüs':
        return '💪';
      case 'sırt':
        return '🏋️';
      case 'omuz':
        return '🤲';
      case 'kol':
        return '💪';
      case 'bacak':
        return '🦵';
      case 'karın':
        return '🔥';
      case 'cardio':
        return '🏃';
      case 'functional':
        return '🤸';
      default:
        return '🏋️‍♂️';
    }
  }
}

/// Üyelik Durumu (Backend: MobileWorkoutProgramController.GetMembershipStatus)
@JsonSerializable()
class MembershipStatus {
  final bool isMember;
  final bool hasActivePrograms;
  final int programCount;
  final String message;

  const MembershipStatus({
    required this.isMember,
    required this.hasActivePrograms,
    required this.programCount,
    required this.message,
  });

  factory MembershipStatus.fromJson(Map<String, dynamic> json) =>
      _$MembershipStatusFromJson(json);

  Map<String, dynamic> toJson() => _$MembershipStatusToJson(this);
}
