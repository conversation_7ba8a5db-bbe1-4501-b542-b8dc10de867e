using System;
using System.Collections.Generic;

namespace Core.CrossCuttingConcerns.Caching.Configuration
{
    public class CacheConfiguration
    {
        public int DefaultDurationMinutes { get; set; } = 60;
        public int MaxCacheSize { get; set; } = 10000;
        public bool EnableStatistics { get; set; } = true;
        public bool EnableDebugLogging { get; set; } = false;
        
        // Tenant bazlı cache ayarları
        public Dictionary<string, EntityCacheSettings> EntitySettings { get; set; } = new();
        
        public CacheConfiguration()
        {
            InitializeDefaultEntitySettings();
        }
        
        private void InitializeDefaultEntitySettings()
        {
            // Member cache ayarları
            EntitySettings["Member"] = new EntityCacheSettings
            {
                DefaultDuration = 30,
                Tags = new[] { "Member", "User" },
                InvalidateOnEntityChange = true,
                RelatedEntities = new[] { "Membership", "Payment" }
            };
            
            // Product cache ayarları
            EntitySettings["Product"] = new EntityCacheSettings
            {
                DefaultDuration = 120,
                Tags = new[] { "Product", "Inventory" },
                InvalidateOnEntityChange = true,
                RelatedEntities = new[] { "Transaction" }
            };
            
            // Payment cache ayarları
            EntitySettings["Payment"] = new EntityCacheSettings
            {
                DefaultDuration = 60,
                Tags = new[] { "Payment", "Financial" },
                InvalidateOnEntityChange = true,
                RelatedEntities = new[] { "Member", "Transaction" }
            };
            
            // Membership cache ayarları
            EntitySettings["Membership"] = new EntityCacheSettings
            {
                DefaultDuration = 45,
                Tags = new[] { "Membership", "Member" },
                InvalidateOnEntityChange = true,
                RelatedEntities = new[] { "Member", "Payment" }
            };
            
            // City/Town cache ayarları (static data)
            EntitySettings["City"] = new EntityCacheSettings
            {
                DefaultDuration = 1440, // 24 saat
                Tags = new[] { "Location", "Static" },
                InvalidateOnEntityChange = false,
                RelatedEntities = new[] { "Town" }
            };
            
            EntitySettings["Town"] = new EntityCacheSettings
            {
                DefaultDuration = 1440, // 24 saat
                Tags = new[] { "Location", "Static" },
                InvalidateOnEntityChange = false,
                RelatedEntities = new[] { "City" }
            };

            // Antrenman Programı Sistemi Cache Ayarları (Mobil Optimizasyonu)
            EntitySettings["WorkoutProgramTemplate"] = new EntityCacheSettings
            {
                DefaultDuration = 120, // 2 saat - programlar sık değişmez
                Tags = new[] { "WorkoutProgram", "Template", "Mobile" },
                InvalidateOnEntityChange = true,
                RelatedEntities = new[] { "WorkoutProgramDay", "WorkoutProgramExercise" }
            };

            EntitySettings["MemberWorkoutProgram"] = new EntityCacheSettings
            {
                DefaultDuration = 30, // 30 dakika - üye atamaları daha dinamik
                Tags = new[] { "MemberWorkout", "Assignment", "Mobile" },
                InvalidateOnEntityChange = true,
                RelatedEntities = new[] { "Member", "WorkoutProgramTemplate" }
            };

            EntitySettings["SystemExercise"] = new EntityCacheSettings
            {
                DefaultDuration = 720, // 12 saat - sistem egzersizleri çok nadir değişir
                Tags = new[] { "Exercise", "System", "Static" },
                InvalidateOnEntityChange = false,
                RelatedEntities = new[] { "ExerciseCategory" }
            };

            EntitySettings["CompanyExercise"] = new EntityCacheSettings
            {
                DefaultDuration = 180, // 3 saat - şirket egzersizleri orta sıklıkta değişir
                Tags = new[] { "Exercise", "Company" },
                InvalidateOnEntityChange = true,
                RelatedEntities = new[] { "ExerciseCategory" }
            };

            EntitySettings["ExerciseCategory"] = new EntityCacheSettings
            {
                DefaultDuration = 1440, // 24 saat - kategoriler çok nadir değişir
                Tags = new[] { "Exercise", "Category", "Static" },
                InvalidateOnEntityChange = false,
                RelatedEntities = new[] { "SystemExercise", "CompanyExercise" }
            };
        }
    }
    
    public class EntityCacheSettings
    {
        public int DefaultDuration { get; set; }
        public string[] Tags { get; set; } = Array.Empty<string>();
        public bool InvalidateOnEntityChange { get; set; } = true;
        public string[] RelatedEntities { get; set; } = Array.Empty<string>();
    }
}
