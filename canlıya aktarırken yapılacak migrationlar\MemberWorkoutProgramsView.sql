-- Üye Program Atama Sistemi View Script
-- Bu script performans için optimize edilmiş view oluşturur

USE [GymProject]
GO

-- CACHE OPTİMİZASYONU İÇİN VİEW (Sık kullanılan join'ler için)
-- Schema bound view oluşturuluyor (index için gerekli)
CREATE VIEW [dbo].[vw_MemberWorkoutProgramDetails]
WITH SCHEMABINDING
AS
SELECT
    mwp.MemberWorkoutProgramID,
    mwp.MemberID,
    m.[Name] AS MemberName,
    m.PhoneNumber AS MemberPhone,
    mwp.WorkoutProgramTemplateID,
    wpt.ProgramName,
    wpt.[Description] AS ProgramDescription,
    wpt.ExperienceLevel,
    wpt.TargetGoal,
    mwp.CompanyID,
    mwp.AssignedDate,
    mwp.StartDate,
    mwp.EndDate,
    mwp.Notes,
    mwp.IsActive,
    mwp.CreationDate
FROM [dbo].[MemberWorkoutPrograms] mwp
INNER JOIN [dbo].[Members] m ON mwp.MemberID = m.MemberID
INNER JOIN [dbo].[WorkoutProgramTemplates] wpt ON mwp.WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID
WHERE mwp.IsActive = 1 AND m.IsActive = 1 AND wpt.IsActive = 1
GO

-- View için clustered index (performans için)
CREATE UNIQUE CLUSTERED INDEX [IX_vw_MemberWorkoutProgramDetails_Clustered]
ON [dbo].[vw_MemberWorkoutProgramDetails] ([MemberWorkoutProgramID])
GO

-- Ek non-clustered indexler (sık kullanılan sorgular için)
CREATE NONCLUSTERED INDEX [IX_vw_MemberWorkoutProgramDetails_MemberID]
ON [dbo].[vw_MemberWorkoutProgramDetails] ([MemberID])
INCLUDE ([WorkoutProgramTemplateID], [ProgramName], [AssignedDate])
GO

CREATE NONCLUSTERED INDEX [IX_vw_MemberWorkoutProgramDetails_CompanyID]
ON [dbo].[vw_MemberWorkoutProgramDetails] ([CompanyID], [IsActive])
INCLUDE ([MemberID], [ProgramName], [StartDate], [EndDate])
GO

-- Ek view: Program istatistikleri ile birlikte (DayCount, ExerciseCount)
-- Bu view schema bound olamaz çünkü subquery içeriyor, ama performans için cache'lenebilir
CREATE VIEW [dbo].[vw_MemberWorkoutProgramStats] AS
SELECT
    mwp.MemberWorkoutProgramID,
    mwp.MemberID,
    m.[Name] AS MemberName,
    m.PhoneNumber AS MemberPhone,
    m.UserID,
    mwp.WorkoutProgramTemplateID,
    wpt.ProgramName,
    wpt.[Description] AS ProgramDescription,
    wpt.ExperienceLevel,
    wpt.TargetGoal,
    mwp.CompanyID,
    mwp.AssignedDate,
    mwp.StartDate,
    mwp.EndDate,
    mwp.Notes,
    mwp.IsActive,
    mwp.CreationDate,
    -- Program gün sayısı
    (SELECT COUNT(*)
     FROM [dbo].[WorkoutProgramDays] wpd
     WHERE wpd.WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID) AS DayCount,
    -- Program egzersiz sayısı
    (SELECT COUNT(*)
     FROM [dbo].[WorkoutProgramExercises] wpe
     INNER JOIN [dbo].[WorkoutProgramDays] wpd ON wpe.WorkoutProgramDayID = wpd.WorkoutProgramDayID
     WHERE wpd.WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID) AS ExerciseCount
FROM [dbo].[MemberWorkoutPrograms] mwp
INNER JOIN [dbo].[Members] m ON mwp.MemberID = m.MemberID
INNER JOIN [dbo].[WorkoutProgramTemplates] wpt ON mwp.WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID
WHERE mwp.IsActive = 1 AND m.IsActive = 1 AND wpt.IsActive = 1
GO

-- Bu view için non-clustered indexler (schema bound olmadığı için clustered index yapamayız)
CREATE NONCLUSTERED INDEX [IX_vw_MemberWorkoutProgramStats_MemberID]
ON [dbo].[vw_MemberWorkoutProgramStats] ([MemberID])
GO

CREATE NONCLUSTERED INDEX [IX_vw_MemberWorkoutProgramStats_UserID]
ON [dbo].[vw_MemberWorkoutProgramStats] ([UserID])
GO

CREATE NONCLUSTERED INDEX [IX_vw_MemberWorkoutProgramStats_CompanyID]
ON [dbo].[vw_MemberWorkoutProgramStats] ([CompanyID], [IsActive])
GO

PRINT 'Üye Program Atama View''ler oluşturuldu!'
PRINT '- vw_MemberWorkoutProgramDetails (Schema bound, indexli)'
PRINT '- vw_MemberWorkoutProgramStats (İstatistikli, mobil API için)'
PRINT 'View indexleri eklendi.'
GO
