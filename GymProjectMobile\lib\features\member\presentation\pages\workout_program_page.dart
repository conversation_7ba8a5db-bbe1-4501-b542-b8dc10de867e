/// Workout Program Page - GymKod Pro Mobile
///
/// Bu sayfa üyelerin antrenman programlarını görüntülemesi için oluşturulmuştur.
/// PROMPT 4A: Mobil API entegrasyonu ile yeniden tasarlandı.
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/core.dart';
import '../../../../shared/widgets/responsive_builder.dart';
import '../../../workout/data/models/workout_program_models.dart';
import '../../../workout/data/services/workout_program_api_service.dart';

/// Workout Program Providers
final workoutProgramApiServiceProvider = Provider<WorkoutProgramApiService>((ref) {
  final apiService = ref.watch(apiServiceProvider);
  return WorkoutProgramApiServiceImpl(apiService);
});

final workoutProgramRepositoryProvider = Provider<WorkoutProgramRepository>((ref) {
  final apiService = ref.watch(workoutProgramApiServiceProvider);
  return WorkoutProgramRepositoryImpl(apiService);
});

/// Workout Program State Provider
final workoutProgramStateProvider = StateNotifierProvider<WorkoutProgramNotifier, WorkoutProgramState>((ref) {
  final repository = ref.watch(workoutProgramRepositoryProvider);
  return WorkoutProgramNotifier(repository);
});

/// Workout Program State
class WorkoutProgramState {
  final bool isLoading;
  final List<MemberActiveWorkoutProgram> programs;
  final MembershipStatus? membershipStatus;
  final String? errorMessage;
  final bool hasPrograms;

  const WorkoutProgramState({
    this.isLoading = false,
    this.programs = const [],
    this.membershipStatus,
    this.errorMessage,
    this.hasPrograms = false,
  });

  WorkoutProgramState copyWith({
    bool? isLoading,
    List<MemberActiveWorkoutProgram>? programs,
    MembershipStatus? membershipStatus,
    String? errorMessage,
    bool? hasPrograms,
  }) {
    return WorkoutProgramState(
      isLoading: isLoading ?? this.isLoading,
      programs: programs ?? this.programs,
      membershipStatus: membershipStatus ?? this.membershipStatus,
      errorMessage: errorMessage,
      hasPrograms: hasPrograms ?? this.hasPrograms,
    );
  }
}

/// Workout Program Notifier
class WorkoutProgramNotifier extends StateNotifier<WorkoutProgramState> {
  final WorkoutProgramRepository _repository;

  WorkoutProgramNotifier(this._repository) : super(const WorkoutProgramState());

  /// Antrenman programlarını yükle
  Future<void> loadWorkoutPrograms() async {
    state = state.copyWith(isLoading: true, errorMessage: null);

    try {
      // Önce üyelik durumunu kontrol et
      final membershipResult = await _repository.getMembershipStatus();

      if (membershipResult.isSuccess && membershipResult.data != null) {
        final membershipStatus = membershipResult.data!;

        if (membershipStatus.hasActivePrograms) {
          // Aktif programları yükle
          final programsResult = await _repository.getMyActivePrograms();

          if (programsResult.isSuccess && programsResult.data != null) {
            state = state.copyWith(
              isLoading: false,
              programs: programsResult.data!,
              membershipStatus: membershipStatus,
              hasPrograms: true,
            );
          } else {
            state = state.copyWith(
              isLoading: false,
              membershipStatus: membershipStatus,
              errorMessage: programsResult.message,
              hasPrograms: false,
            );
          }
        } else {
          // Program yok
          state = state.copyWith(
            isLoading: false,
            membershipStatus: membershipStatus,
            hasPrograms: false,
          );
        }
      } else {
        state = state.copyWith(
          isLoading: false,
          errorMessage: membershipResult.message,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Antrenman programları yüklenirken bir hata oluştu.',
      );
    }
  }

  /// Programları yenile
  Future<void> refreshPrograms() async {
    await loadWorkoutPrograms();
  }
}

/// Workout Program Page
/// Member rolündeki kullanıcılar için antrenman programı sayfası
class WorkoutProgramPage extends ConsumerStatefulWidget {
  const WorkoutProgramPage({super.key});

  @override
  ConsumerState<WorkoutProgramPage> createState() => _WorkoutProgramPageState();
}

class _WorkoutProgramPageState extends ConsumerState<WorkoutProgramPage> {
  @override
  void initState() {
    super.initState();

    // Sayfa açıldığında antrenman programlarını yükle
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(workoutProgramStateProvider.notifier).loadWorkoutPrograms();
      LoggingService.info('Workout program page loaded', tag: 'WORKOUT');
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final workoutState = ref.watch(workoutProgramStateProvider);

    return ResponsiveBuilder(
      builder: (context, deviceType) {
        return Scaffold(
          // Responsive Body
          body: ResponsiveContainer(
            child: Container(
              width: double.infinity,
              height: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    theme.colorScheme.primary,
                    theme.colorScheme.primary.withValues(alpha: 0.8),
                    theme.colorScheme.surface,
                  ],
                ),
              ),
              child: SafeArea(
                child: Column(
                  children: [
                    // Responsive Content Section
                    Expanded(
                      child: Padding(
                        padding: AppSpacing.responsiveScreenPadding(context),
                        child: _buildResponsiveWorkoutContent(theme, deviceType, workoutState),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// Responsive Workout Content
  Widget _buildResponsiveWorkoutContent(ThemeData theme, DeviceType deviceType, WorkoutProgramState workoutState) {
    return RefreshIndicator(
      onRefresh: () async {
        await ref.read(workoutProgramStateProvider.notifier).refreshPrograms();
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          children: [
            ResponsiveSpacing.vertical(
              mobile: 16.0,
              tablet: 20.0,
              desktop: 24.0,
            ),

            // Loading State
            if (workoutState.isLoading)
              _buildLoadingState(theme, deviceType),

            // Error State
            if (!workoutState.isLoading && workoutState.errorMessage != null)
              _buildErrorState(theme, deviceType, workoutState.errorMessage!),

            // No Programs State
            if (!workoutState.isLoading &&
                workoutState.errorMessage == null &&
                !workoutState.hasPrograms)
              _buildNoProgramsState(theme, deviceType, workoutState.membershipStatus),

            // Programs List
            if (!workoutState.isLoading &&
                workoutState.errorMessage == null &&
                workoutState.hasPrograms)
              _buildProgramsList(theme, deviceType, workoutState.programs),
          ],
        ),
      ),
    );
  }

  /// Loading State
  Widget _buildLoadingState(ThemeData theme, DeviceType deviceType) {
    return ResponsiveCard(
      padding: AppSpacing.responsiveCardPadding(context),
      child: Column(
        children: [
          CircularProgressIndicator(
            color: theme.colorScheme.primary,
          ),
          ResponsiveSpacing.vertical(
            mobile: 16.0,
            tablet: 20.0,
            desktop: 24.0,
          ),
          ResponsiveText(
            'Antrenman programlarınız yükleniyor...',
            textType: 'bodymedium',
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Error State
  Widget _buildErrorState(ThemeData theme, DeviceType deviceType, String errorMessage) {
    return ResponsiveCard(
      padding: AppSpacing.responsiveCardPadding(context),
      child: Column(
        children: [
          Icon(
            Icons.error_outline,
            size: AppSpacing.responsiveIconSize(context,
              mobile: 48.0,
              tablet: 56.0,
              desktop: 64.0,
            ),
            color: theme.colorScheme.error,
          ),
          ResponsiveSpacing.vertical(
            mobile: 16.0,
            tablet: 20.0,
            desktop: 24.0,
          ),
          ResponsiveText(
            'Hata',
            textType: 'h3',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.error,
            ),
          ),
          ResponsiveSpacing.vertical(
            mobile: 8.0,
            tablet: 10.0,
            desktop: 12.0,
          ),
          ResponsiveText(
            errorMessage,
            textType: 'bodymedium',
            textAlign: TextAlign.center,
          ),
          ResponsiveSpacing.vertical(
            mobile: 16.0,
            tablet: 20.0,
            desktop: 24.0,
          ),
          ElevatedButton.icon(
            onPressed: () {
              ref.read(workoutProgramStateProvider.notifier).refreshPrograms();
            },
            icon: const Icon(Icons.refresh),
            label: const Text('Tekrar Dene'),
          ),
        ],
      ),
    );
  }

  /// No Programs State
  Widget _buildNoProgramsState(ThemeData theme, DeviceType deviceType, MembershipStatus? membershipStatus) {
    return ResponsiveCard(
      padding: AppSpacing.responsiveCardPadding(context),
      child: Column(
        children: [
          Icon(
            Icons.assignment_outlined,
            size: AppSpacing.responsiveIconSize(context,
              mobile: 64.0,
              tablet: 72.0,
              desktop: 80.0,
            ),
            color: theme.colorScheme.primary,
          ),
          ResponsiveSpacing.vertical(
            mobile: 20.0,
            tablet: 24.0,
            desktop: 28.0,
          ),
          ResponsiveText(
            'Antrenman Programı',
            textType: 'h2',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          ResponsiveSpacing.vertical(
            mobile: 12.0,
            tablet: 16.0,
            desktop: 20.0,
          ),
          ResponsiveText(
            membershipStatus?.message ?? 'Henüz size atanmış bir antrenman programı bulunmuyor. Salon yöneticinize danışabilirsiniz.',
            textType: 'bodymedium',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          ResponsiveSpacing.vertical(
            mobile: 24.0,
            tablet: 28.0,
            desktop: 32.0,
          ),
          Container(
            padding: EdgeInsets.all(AppSpacing.responsive(context,
              mobile: 16.0,
              tablet: 20.0,
              desktop: 24.0,
            )),
            decoration: BoxDecoration(
              color: theme.colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(AppSpacing.responsiveBorderRadius(context,
                mobile: 12.0,
                tablet: 16.0,
                desktop: 20.0,
              )),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: theme.colorScheme.onPrimaryContainer,
                  size: AppSpacing.responsiveIconSize(context,
                    mobile: 20.0,
                    tablet: 24.0,
                    desktop: 28.0,
                  ),
                ),
                ResponsiveSpacing.horizontal(
                  mobile: 12.0,
                  tablet: 16.0,
                  desktop: 20.0,
                ),
                Expanded(
                  child: ResponsiveText(
                    'Antrenman programı atanması için salon yöneticiniz ile iletişime geçebilirsiniz.',
                    textType: 'bodysmall',
                    style: TextStyle(
                      color: theme.colorScheme.onPrimaryContainer,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Programs List
  Widget _buildProgramsList(ThemeData theme, DeviceType deviceType, List<MemberActiveWorkoutProgram> programs) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        ResponsiveText(
          'Aktif Antrenman Programlarınız',
          textType: 'h2',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        ResponsiveSpacing.vertical(
          mobile: 16.0,
          tablet: 20.0,
          desktop: 24.0,
        ),

        // Programs
        ...programs.map((program) => Padding(
          padding: EdgeInsets.only(
            bottom: AppSpacing.responsive(context,
              mobile: 16.0,
              tablet: 20.0,
              desktop: 24.0,
            ),
          ),
          child: _buildProgramCard(theme, deviceType, program),
        )),
      ],
    );
  }

  /// Program Card
  Widget _buildProgramCard(ThemeData theme, DeviceType deviceType, MemberActiveWorkoutProgram program) {
    return ResponsiveCard(
      padding: AppSpacing.responsiveCardPadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Program Header
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(AppSpacing.responsive(context,
                  mobile: 8.0,
                  tablet: 10.0,
                  desktop: 12.0,
                )),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary,
                  borderRadius: BorderRadius.circular(AppSpacing.responsiveBorderRadius(context,
                    mobile: 8.0,
                    tablet: 10.0,
                    desktop: 12.0,
                  )),
                ),
                child: Icon(
                  Icons.fitness_center,
                  color: theme.colorScheme.onPrimary,
                  size: AppSpacing.responsiveIconSize(context,
                    mobile: 20.0,
                    tablet: 24.0,
                    desktop: 28.0,
                  ),
                ),
              ),
              ResponsiveSpacing.horizontal(
                mobile: 12.0,
                tablet: 16.0,
                desktop: 20.0,
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ResponsiveText(
                      program.programName,
                      textType: 'cardtitle',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (program.programDescription != null) ...[
                      ResponsiveSpacing.vertical(
                        mobile: 4.0,
                        tablet: 6.0,
                        desktop: 8.0,
                      ),
                      ResponsiveText(
                        program.programDescription!,
                        textType: 'bodysmall',
                        style: TextStyle(
                          color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),

          ResponsiveSpacing.vertical(
            mobile: 16.0,
            tablet: 20.0,
            desktop: 24.0,
          ),

          // Program Info
          _buildProgramInfo(theme, deviceType, program),

          ResponsiveSpacing.vertical(
            mobile: 16.0,
            tablet: 20.0,
            desktop: 24.0,
          ),

          // Action Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () {
                // TODO: Program detay sayfasına git
                LoggingService.info('Program detail requested: ${program.programName}', tag: 'WORKOUT');
              },
              icon: const Icon(Icons.visibility),
              label: const Text('Programı Görüntüle'),
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(
                  vertical: AppSpacing.responsive(context,
                    mobile: 12.0,
                    tablet: 16.0,
                    desktop: 20.0,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Program Info
  Widget _buildProgramInfo(ThemeData theme, DeviceType deviceType, MemberActiveWorkoutProgram program) {
    return Row(
      children: [
        // Days Count
        Expanded(
          child: _buildInfoItem(
            theme,
            deviceType,
            Icons.calendar_today,
            '${program.dayCount} Gün',
            'Program Süresi',
          ),
        ),

        ResponsiveSpacing.horizontal(
          mobile: 16.0,
          tablet: 20.0,
          desktop: 24.0,
        ),

        // Exercise Count
        Expanded(
          child: _buildInfoItem(
            theme,
            deviceType,
            Icons.fitness_center,
            '${program.exerciseCount} Egzersiz',
            'Toplam Egzersiz',
          ),
        ),

        ResponsiveSpacing.horizontal(
          mobile: 16.0,
          tablet: 20.0,
          desktop: 24.0,
        ),

        // Experience Level
        Expanded(
          child: _buildInfoItem(
            theme,
            deviceType,
            Icons.trending_up,
            program.experienceLevel ?? 'Belirtilmemiş',
            'Seviye',
          ),
        ),
      ],
    );
  }

  /// Info Item
  Widget _buildInfoItem(ThemeData theme, DeviceType deviceType, IconData icon, String value, String label) {
    return Column(
      children: [
        Icon(
          icon,
          color: theme.colorScheme.primary,
          size: AppSpacing.responsiveIconSize(context,
            mobile: 20.0,
            tablet: 24.0,
            desktop: 28.0,
          ),
        ),
        ResponsiveSpacing.vertical(
          mobile: 4.0,
          tablet: 6.0,
          desktop: 8.0,
        ),
        ResponsiveText(
          value,
          textType: 'labelmedium',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        ResponsiveSpacing.vertical(
          mobile: 2.0,
          tablet: 3.0,
          desktop: 4.0,
        ),
        ResponsiveText(
          label,
          textType: 'bodysmall',
          style: TextStyle(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
