using Business.Abstract;
using Core.Utilities.Security.JWT;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace WebAPI.Controllers
{
    [Route("api/mobile/[controller]")]
    [ApiController]
    [Authorize] // Mobil API'ler için authentication gerekli
    public class MobileWorkoutProgramController : ControllerBase
    {
        private readonly IMemberWorkoutProgramService _memberWorkoutProgramService;
        private readonly IWorkoutProgramTemplateService _workoutProgramTemplateService;
        private readonly ITokenHelper _tokenHelper;

        public MobileWorkoutProgramController(
            IMemberWorkoutProgramService memberWorkoutProgramService,
            IWorkoutProgramTemplateService workoutProgramTemplateService,
            ITokenHelper tokenHelper)
        {
            _memberWorkoutProgramService = memberWorkoutProgramService;
            _workoutProgramTemplateService = workoutProgramTemplateService;
            _tokenHelper = tokenHelper;
        }

        /// <summary>
        /// Mobil: Kullanıcının aktif antrenman programlarını getirir
        /// User token'ından UserID alınır, Member ilişkisi üzerinden programlar getirilir
        /// </summary>
        [HttpGet("my-programs")]
        public IActionResult GetMyActivePrograms()
        {
            try
            {
                // Token'dan UserID'yi al
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
                {
                    return BadRequest(new { success = false, message = "Geçersiz kullanıcı token'ı." });
                }

                // User ID üzerinden aktif programları getir
                var result = _memberWorkoutProgramService.GetActiveWorkoutProgramsByUserId(userId);
                
                if (result.Success)
                {
                    return Ok(new
                    {
                        success = true,
                        message = result.Message,
                        data = result.Data,
                        hasPrograms = result.Data?.Count > 0
                    });
                }

                return BadRequest(new { success = false, message = result.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false, 
                    message = "Antrenman programları alınırken bir hata oluştu.",
                    error = ex.Message 
                });
            }
        }

        /// <summary>
        /// Mobil: Belirli bir antrenman programının detaylarını getirir (günler + egzersizler)
        /// </summary>
        [HttpGet("program-detail/{programTemplateId}")]
        public IActionResult GetProgramDetail(int programTemplateId)
        {
            try
            {
                // Token'dan UserID'yi al
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
                {
                    return BadRequest(new { success = false, message = "Geçersiz kullanıcı token'ı." });
                }

                // Önce kullanıcının bu programa erişim hakkı olup olmadığını kontrol et
                var userPrograms = _memberWorkoutProgramService.GetActiveWorkoutProgramsByUserId(userId);
                if (!userPrograms.Success || !userPrograms.Data.Any(p => p.WorkoutProgramTemplateID == programTemplateId))
                {
                    return Forbid("Bu programa erişim yetkiniz bulunmuyor.");
                }

                // Program detaylarını getir
                var result = _workoutProgramTemplateService.GetById(programTemplateId);
                
                if (result.Success)
                {
                    return Ok(new
                    {
                        success = true,
                        message = result.Message,
                        data = result.Data
                    });
                }

                return BadRequest(new { success = false, message = result.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false, 
                    message = "Program detayları alınırken bir hata oluştu.",
                    error = ex.Message 
                });
            }
        }

        /// <summary>
        /// Mobil: Kullanıcının üyelik durumunu kontrol eder
        /// Eğer Member kaydı yoksa veya aktif değilse uygun mesaj döner
        /// </summary>
        [HttpGet("membership-status")]
        public IActionResult GetMembershipStatus()
        {
            try
            {
                // Token'dan UserID'yi al
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
                {
                    return BadRequest(new { success = false, message = "Geçersiz kullanıcı token'ı." });
                }

                // User ID üzerinden aktif programları getir
                var result = _memberWorkoutProgramService.GetActiveWorkoutProgramsByUserId(userId);
                
                if (result.Success)
                {
                    bool hasActivePrograms = result.Data?.Count > 0;
                    
                    return Ok(new
                    {
                        success = true,
                        data = new
                        {
                            isMember = true,
                            hasActivePrograms = hasActivePrograms,
                            programCount = result.Data?.Count ?? 0,
                            message = hasActivePrograms 
                                ? "Aktif antrenman programlarınız bulunuyor." 
                                : "Henüz size atanmış bir antrenman programı bulunmuyor. Salon yöneticinize danışabilirsiniz."
                        }
                    });
                }

                // Eğer result başarısızsa, muhtemelen Member kaydı yok
                return Ok(new
                {
                    success = true,
                    data = new
                    {
                        isMember = false,
                        hasActivePrograms = false,
                        programCount = 0,
                        message = "Üyelik kaydınız bulunamadı. Salon yöneticinize danışınız."
                    }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new 
                { 
                    success = false, 
                    message = "Üyelik durumu kontrol edilirken bir hata oluştu.",
                    error = ex.Message 
                });
            }
        }
    }
}
