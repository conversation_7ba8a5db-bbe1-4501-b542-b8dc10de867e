/// Workout Program Page - GymKod Pro Mobile
///
/// Bu sayfa üyelerin antrenman programlarını görüntülemesi için oluşturulmuştur.
/// PROMPT 4A: Mobil API entegrasyonu ile yeniden tasarlandı.
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/core.dart';
import '../../../../shared/widgets/responsive_builder.dart';

/// Workout Program Page
/// Member rolündeki kullanıcılar için antrenman programı sayfası
class WorkoutProgramPage extends ConsumerStatefulWidget {
  const WorkoutProgramPage({super.key});

  @override
  ConsumerState<WorkoutProgramPage> createState() => _WorkoutProgramPageState();
}

class _WorkoutProgramPageState extends ConsumerState<WorkoutProgramPage> {
  @override
  void initState() {
    super.initState();

    // <PERSON><PERSON> açıldığında antrenman programlarını yükle
    WidgetsBinding.instance.addPostFrameCallback((_) {
      LoggingService.info('Workout program page loaded', tag: 'WORKOUT');
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return ResponsiveBuilder(
      builder: (context, deviceType) {
        return Scaffold(
          // Responsive Body
          body: ResponsiveContainer(
            child: Container(
              width: double.infinity,
              height: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    theme.colorScheme.primary,
                    theme.colorScheme.primary.withValues(alpha: 0.8),
                    theme.colorScheme.surface,
                  ],
                ),
              ),
              child: SafeArea(
                child: Column(
                  children: [
                    // Responsive Content Section
                    Expanded(
                      child: Padding(
                        padding: AppSpacing.responsiveScreenPadding(context),
                        child: _buildResponsiveWorkoutContent(theme, deviceType),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// Responsive Workout Content
  Widget _buildResponsiveWorkoutContent(ThemeData theme, DeviceType deviceType) {
    return SingleChildScrollView(
      child: Column(
        children: [
          ResponsiveSpacing.vertical(
            mobile: 16.0,
            tablet: 20.0,
            desktop: 24.0,
          ),

          // Geçici içerik - API entegrasyonu için hazır
          _buildTemporaryContent(theme, deviceType),
        ],
      ),
    );
  }

  /// Geçici İçerik
  Widget _buildTemporaryContent(ThemeData theme, DeviceType deviceType) {
    return ResponsiveCard(
      padding: AppSpacing.responsiveCardPadding(context),
      child: Column(
        children: [
          Icon(
            Icons.assignment_outlined,
            size: AppSpacing.responsiveIconSize(context,
              mobile: 64.0,
              tablet: 72.0,
              desktop: 80.0,
            ),
            color: theme.colorScheme.primary,
          ),
          ResponsiveSpacing.vertical(
            mobile: 20.0,
            tablet: 24.0,
            desktop: 28.0,
          ),
          ResponsiveText(
            'Antrenman Programı',
            textType: 'h2',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          ResponsiveSpacing.vertical(
            mobile: 12.0,
            tablet: 16.0,
            desktop: 20.0,
          ),
          ResponsiveText(
            'Henüz size atanmış bir antrenman programı bulunmuyor. Salon yöneticinize danışabilirsiniz.',
            textType: 'bodymedium',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          ResponsiveSpacing.vertical(
            mobile: 24.0,
            tablet: 28.0,
            desktop: 32.0,
          ),
          Container(
            padding: EdgeInsets.all(AppSpacing.responsive(context,
              mobile: 16.0,
              tablet: 20.0,
              desktop: 24.0,
            )),
            decoration: BoxDecoration(
              color: theme.colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(AppSpacing.responsiveBorderRadius(context,
                mobile: 12.0,
                tablet: 16.0,
                desktop: 20.0,
              )),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: theme.colorScheme.onPrimaryContainer,
                  size: AppSpacing.responsiveIconSize(context,
                    mobile: 20.0,
                    tablet: 24.0,
                    desktop: 28.0,
                  ),
                ),
                ResponsiveSpacing.horizontal(
                  mobile: 12.0,
                  tablet: 16.0,
                  desktop: 20.0,
                ),
                Expanded(
                  child: ResponsiveText(
                    'PROMPT 4A: Mobil API entegrasyonu hazır. Backend API\'ler tamamlandı.',
                    textType: 'bodysmall',
                    style: TextStyle(
                      color: theme.colorScheme.onPrimaryContainer,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
